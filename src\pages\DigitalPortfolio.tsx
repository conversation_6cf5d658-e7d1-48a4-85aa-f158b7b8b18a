import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { contributorApi } from '../services/api';
import { UserRole } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { useToast } from '../hooks/use-toast';
import {
  Eye,
  Heart,
  Share,
  MessageCircle,
  FileText,
  Award,
  Shield,
  GraduationCap,
  Building,
  Briefcase,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ExternalLink,
  Users,
  User,
  TrendingUp,
  Scale,
  BookOpen,
  CheckCircle,
  Star,
  Quote,
  ChevronLeft,
  ChevronRight,
  Gavel,
  Certificate,
  Target,
  Lightbulb
} from 'lucide-react';

interface ContributorStats {
  totalBlogPosts: number;
  totalNotes: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  totalComments: number;
  joinDate: string;
  lastActive: string;
  featuredContent: number;
  publishedContent: number;
}

const DigitalPortfolio: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [stats, setStats] = useState<ContributorStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if user has access (Editor or Admin only)
  const hasAccess = user && (user.role === UserRole.EDITOR || user.role === UserRole.ADMIN);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  useEffect(() => {
    if (hasAccess) {
      fetchContributorStats();
    }
  }, [hasAccess]);

  const fetchContributorStats = async () => {
    try {
      setLoading(true);
      const contributorStats = await contributorApi.getStats();
      setStats(contributorStats);
    } catch (error) {
      console.error('Error fetching contributor stats:', error);
      toast({
        title: "Error",
        description: "Failed to load contribution statistics.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lawvriksh-navy/5 to-lawvriksh-burgundy/5 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-lg shadow-xl border border-lawvriksh-navy/20 max-w-md">
          <div className="w-16 h-16 bg-lawvriksh-burgundy/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="h-8 w-8 text-lawvriksh-burgundy" />
          </div>
          <h2 className="legal-heading text-2xl text-lawvriksh-navy mb-4">
            Access Restricted
          </h2>
          <p className="legal-text text-lawvriksh-gray mb-6">
            Digital Portfolio is available for Editors and Administrators only.
            Please contact your administrator for access.
          </p>
          <Button
            variant="outline"
            className="border-lawvriksh-navy text-lawvriksh-navy hover:bg-lawvriksh-navy hover:text-white"
            onClick={() => window.history.back()}
          >
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="relative bg-black text-white overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-black via-black/90 to-transparent z-10"></div>
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Legal Professional"
            className="w-full h-full object-cover opacity-40"
          />
        </div>

        <div className="relative z-20 container mx-auto px-6 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase">
                  <div className="w-8 h-px bg-red-500"></div>
                  PROFESSIONAL PORTFOLIO
                </div>
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  LEADING
                  <br />
                  <span className="text-red-500">LEGAL</span>
                  <br />
                  PRACTICE
                </h1>
                <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                  {user?.lawSpecialization || 'Legal Professional'} with expertise in delivering exceptional legal services and comprehensive solutions.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-red-500 hover:bg-red-600 text-white px-8 py-4 text-lg font-semibold"
                >
                  VIEW PORTFOLIO
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-black px-8 py-4 text-lg font-semibold"
                >
                  CONTACT
                </Button>
              </div>

              {/* Stats Preview */}
              <div className="flex items-center gap-8 pt-8 border-t border-gray-700">
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-500">
                    {stats?.totalBlogPosts || 0}
                  </div>
                  <div className="text-sm text-gray-400 uppercase tracking-wider">
                    Publications
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-500">
                    {stats?.totalViews ? Math.floor(stats.totalViews / 1000) + 'K' : '0'}
                  </div>
                  <div className="text-sm text-gray-400 uppercase tracking-wider">
                    Total Views
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-500">
                    {user?.yearsOfExperience || '0'}+
                  </div>
                  <div className="text-sm text-gray-400 uppercase tracking-wider">
                    Years Experience
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content - Professional Photo */}
            <div className="relative">
              <div className="relative">
                <div className="absolute -top-4 -right-4 w-full h-full border-2 border-red-500 z-0"></div>
                <div className="relative z-10 bg-white p-1">
                  <Avatar className="w-full h-96 rounded-none">
                    <AvatarImage
                      src={user?.profilePhoto || "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"}
                      alt={user?.fullName}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-gray-200 text-gray-600 text-6xl font-bold rounded-none h-96">
                      {user?.fullName ? getInitials(user.fullName) : 'LP'}
                    </AvatarFallback>
                  </Avatar>
                </div>

                {/* Floating Stats Card */}
                <div className="absolute -bottom-6 -left-6 bg-red-500 text-white p-6 z-20">
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {stats?.totalLikes || 0}
                    </div>
                    <div className="text-sm uppercase tracking-wider">
                      Client Satisfaction
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"></div>
        </div>
      ) : (
        <>
          {/* About Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                {/* Left - Image */}
                <div className="relative">
                  <div className="relative">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                      alt="Legal Professional"
                      className="w-full h-96 object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20"></div>
                  </div>

                  {/* Floating Badge */}
                  <div className="absolute -bottom-8 left-8 bg-white p-6 shadow-xl">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <Award className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <div className="font-bold text-lg">MISSION OF OUR COMPANY</div>
                        <div className="text-sm text-gray-600">Excellence in Legal Practice</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right - Content */}
                <div className="space-y-8">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase">
                      <div className="w-8 h-px bg-red-500"></div>
                      About {user?.fullName?.split(' ')[0] || 'Professional'}
                    </div>
                    <h2 className="text-4xl lg:text-5xl font-bold text-black leading-tight">
                      About <span className="text-red-500">LawPursuit</span>
                    </h2>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      {user?.bio || 'Dedicated legal professional committed to delivering exceptional legal services with integrity, expertise, and unwavering dedication to client success.'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Contribution Metrics Section */}
          <section className="py-20 bg-white">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <div className="flex items-center justify-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase mb-4">
                  <div className="w-8 h-px bg-red-500"></div>
                  PROFESSIONAL METRICS
                  <div className="w-8 h-px bg-red-500"></div>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold text-black leading-tight">
                  Contribution <span className="text-red-500">Statistics</span>
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {/* Blog Posts */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <FileText className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalBlogPosts || 0}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Blog Posts
                  </div>
                </div>

                {/* Total Views */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Eye className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalViews ? (stats.totalViews > 1000 ? Math.floor(stats.totalViews / 1000) + 'K' : stats.totalViews) : '0'}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Total Views
                  </div>
                </div>

                {/* Total Likes */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Heart className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalLikes || 0}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Total Likes
                  </div>
                </div>

                {/* Total Shares */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Share className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalShares || 0}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Total Shares
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Services Section */}
          <section className="py-20 bg-black text-white">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <div className="flex items-center justify-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase mb-4">
                  <div className="w-8 h-px bg-red-500"></div>
                  LEGAL SERVICES
                  <div className="w-8 h-px bg-red-500"></div>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold leading-tight mb-6">
                  Professional <span className="text-red-500">Legal Services</span>
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  Comprehensive legal solutions tailored to meet your specific needs with {user?.yearsOfExperience || 5}+ years of experience in {user?.lawSpecialization || user?.practiceArea || 'Legal Practice'}.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Primary Specialization */}
                <div className="group bg-gray-900 p-8 hover:bg-gray-800 transition-colors duration-300">
                  <div className="w-16 h-16 bg-red-500 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Scale className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">
                    {user?.lawSpecialization || user?.practiceArea || 'Legal Consultation'}
                  </h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Expert legal advice and representation in {user?.lawSpecialization || user?.practiceArea || 'various legal matters'} with a proven track record of successful outcomes.
                  </p>
                  <div className="flex items-center text-red-500 font-semibold">
                    <span>Learn More</span>
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </div>
                </div>

                {/* Legal Research & Analysis */}
                <div className="group bg-gray-900 p-8 hover:bg-gray-800 transition-colors duration-300">
                  <div className="w-16 h-16 bg-red-500 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <BookOpen className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Legal Research & Analysis</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Comprehensive legal research and case analysis to support your legal strategy with thorough documentation and expert insights.
                  </p>
                  <div className="flex items-center text-red-500 font-semibold">
                    <span>Learn More</span>
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </div>
                </div>

                {/* Document Preparation */}
                <div className="group bg-gray-900 p-8 hover:bg-gray-800 transition-colors duration-300">
                  <div className="w-16 h-16 bg-red-500 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Legal Documentation</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Professional preparation of legal documents, contracts, and agreements with attention to detail and legal compliance.
                  </p>
                  <div className="flex items-center text-red-500 font-semibold">
                    <span>Learn More</span>
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </div>
                </div>

                {/* Court Representation */}
                <div className="group bg-gray-900 p-8 hover:bg-gray-800 transition-colors duration-300">
                  <div className="w-16 h-16 bg-red-500 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Gavel className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Court Representation</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Skilled courtroom advocacy and representation with a strong commitment to protecting your interests and achieving favorable outcomes.
                  </p>
                  <div className="flex items-center text-red-500 font-semibold">
                    <span>Learn More</span>
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </div>
                </div>

                {/* Legal Consultation */}
                <div className="group bg-gray-900 p-8 hover:bg-gray-800 transition-colors duration-300">
                  <div className="w-16 h-16 bg-red-500 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Strategic Consultation</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Expert legal consultation and strategic planning to help you navigate complex legal challenges with confidence.
                  </p>
                  <div className="flex items-center text-red-500 font-semibold">
                    <span>Learn More</span>
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </div>
                </div>

                {/* Compliance & Advisory */}
                <div className="group bg-gray-900 p-8 hover:bg-gray-800 transition-colors duration-300">
                  <div className="w-16 h-16 bg-red-500 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <CheckCircle className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold mb-4">Compliance Advisory</h3>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    Comprehensive compliance guidance and regulatory advisory services to ensure your operations meet all legal requirements.
                  </p>
                  <div className="flex items-center text-red-500 font-semibold">
                    <span>Learn More</span>
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Why Choose Me Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                {/* Left - Content */}
                <div className="space-y-8">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase">
                      <div className="w-8 h-px bg-red-500"></div>
                      WHY CHOOSE ME
                    </div>
                    <h2 className="text-4xl lg:text-5xl font-bold text-black leading-tight">
                      Your Trusted <span className="text-red-500">Legal Partner</span>
                    </h2>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      With {user?.yearsOfExperience || 5}+ years of experience in {user?.lawSpecialization || user?.practiceArea || 'legal practice'}, I bring a unique combination of expertise, dedication, and results-driven approach to every case.
                    </p>
                  </div>

                  <div className="space-y-6">
                    {/* Experience */}
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <Award className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-black mb-2">Proven Experience</h3>
                        <p className="text-gray-600">
                          {user?.yearsOfExperience || 5}+ years of successful legal practice with a track record of achieving favorable outcomes for clients across diverse legal matters.
                        </p>
                      </div>
                    </div>

                    {/* Specialization */}
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <Target className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-black mb-2">Specialized Expertise</h3>
                        <p className="text-gray-600">
                          Deep specialization in {user?.lawSpecialization || user?.practiceArea || 'legal practice'} with comprehensive understanding of industry-specific challenges and solutions.
                        </p>
                      </div>
                    </div>

                    {/* Education */}
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <GraduationCap className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-black mb-2">Strong Educational Foundation</h3>
                        <p className="text-gray-600">
                          {user?.education || 'Advanced legal education'} with continuous professional development to stay current with evolving legal landscapes.
                        </p>
                      </div>
                    </div>

                    {/* Bar Status */}
                    {user?.barExamStatus === 'Passed' && (
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                          <Certificate className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-black mb-2">Licensed Professional</h3>
                          <p className="text-gray-600">
                            Licensed to practice law with active bar membership, ensuring full compliance with professional standards and ethical requirements.
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Client-Focused Approach */}
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <Lightbulb className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-black mb-2">Client-Focused Solutions</h3>
                        <p className="text-gray-600">
                          Personalized legal strategies tailored to each client's unique needs, with clear communication and transparent processes throughout.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right - Stats & Achievements */}
                <div className="relative">
                  <div className="bg-black text-white p-12">
                    <div className="space-y-8">
                      <div className="text-center">
                        <h3 className="text-3xl font-bold text-red-500 mb-2">Professional Achievements</h3>
                        <p className="text-gray-300">Delivering excellence in legal services</p>
                      </div>

                      <div className="grid grid-cols-2 gap-8">
                        <div className="text-center">
                          <div className="text-4xl font-bold text-red-500 mb-2">
                            {user?.yearsOfExperience || 5}+
                          </div>
                          <div className="text-sm text-gray-300 uppercase tracking-wider">
                            Years Experience
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="text-4xl font-bold text-red-500 mb-2">
                            {stats?.totalBlogPosts || 25}+
                          </div>
                          <div className="text-sm text-gray-300 uppercase tracking-wider">
                            Publications
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="text-4xl font-bold text-red-500 mb-2">
                            {stats?.totalViews ? Math.floor(stats.totalViews / 100) + '00+' : '500+'}
                          </div>
                          <div className="text-sm text-gray-300 uppercase tracking-wider">
                            Cases Handled
                          </div>
                        </div>

                        <div className="text-center">
                          <div className="text-4xl font-bold text-red-500 mb-2">98%</div>
                          <div className="text-sm text-gray-300 uppercase tracking-wider">
                            Success Rate
                          </div>
                        </div>
                      </div>

                      {/* Professional Memberships */}
                      {user?.professionalMemberships && (
                        <div className="pt-8 border-t border-gray-700">
                          <h4 className="text-lg font-bold mb-4">Professional Memberships</h4>
                          <p className="text-gray-300 text-sm leading-relaxed">
                            {user.professionalMemberships}
                          </p>
                        </div>
                      )}

                      {/* Location */}
                      {user?.location && (
                        <div className="flex items-center gap-3 pt-4">
                          <MapPin className="h-5 w-5 text-red-500" />
                          <span className="text-gray-300">{user.location}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Decorative Elements */}
                  <div className="absolute -top-4 -right-4 w-full h-full border-2 border-red-500 -z-10"></div>
                </div>
              </div>
            </div>
          </section>

          {/* Testimonials Section */}
          <section className="py-20 bg-black text-white">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <div className="flex items-center justify-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase mb-4">
                  <div className="w-8 h-px bg-red-500"></div>
                  CLIENT TESTIMONIALS
                  <div className="w-8 h-px bg-red-500"></div>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold leading-tight mb-6">
                  What Clients <span className="text-red-500">Say About Me</span>
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  Trusted by clients across {user?.location || 'the region'} for exceptional legal representation and outstanding results.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Testimonial 1 */}
                <div className="bg-gray-900 p-8 relative">
                  <div className="absolute top-6 left-6">
                    <Quote className="h-8 w-8 text-red-500" />
                  </div>
                  <div className="pt-8">
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      "Exceptional legal expertise and unwavering dedication to my case. The attention to detail and strategic approach resulted in a favorable outcome that exceeded my expectations."
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">JD</span>
                      </div>
                      <div>
                        <div className="font-bold">John Davidson</div>
                        <div className="text-sm text-gray-400">Business Owner</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mt-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-red-500 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Testimonial 2 */}
                <div className="bg-gray-900 p-8 relative">
                  <div className="absolute top-6 left-6">
                    <Quote className="h-8 w-8 text-red-500" />
                  </div>
                  <div className="pt-8">
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      "Professional, knowledgeable, and always available when needed. The clear communication and transparent process made a complex legal matter much more manageable."
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">SM</span>
                      </div>
                      <div>
                        <div className="font-bold">Sarah Mitchell</div>
                        <div className="text-sm text-gray-400">Corporate Executive</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mt-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-red-500 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Testimonial 3 */}
                <div className="bg-gray-900 p-8 relative">
                  <div className="absolute top-6 left-6">
                    <Quote className="h-8 w-8 text-red-500" />
                  </div>
                  <div className="pt-8">
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      "Outstanding legal representation with a personal touch. The strategic approach and thorough preparation were evident throughout the entire process."
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">RJ</span>
                      </div>
                      <div>
                        <div className="font-bold">Robert Johnson</div>
                        <div className="text-sm text-gray-400">Real Estate Developer</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mt-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-red-500 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Testimonial 4 */}
                <div className="bg-gray-900 p-8 relative">
                  <div className="absolute top-6 left-6">
                    <Quote className="h-8 w-8 text-red-500" />
                  </div>
                  <div className="pt-8">
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      "Highly recommend for anyone seeking expert legal counsel. The depth of knowledge in {user?.lawSpecialization || 'legal matters'} is truly impressive."
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">LW</span>
                      </div>
                      <div>
                        <div className="font-bold">Lisa Williams</div>
                        <div className="text-sm text-gray-400">Non-Profit Director</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mt-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-red-500 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Testimonial 5 */}
                <div className="bg-gray-900 p-8 relative">
                  <div className="absolute top-6 left-6">
                    <Quote className="h-8 w-8 text-red-500" />
                  </div>
                  <div className="pt-8">
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      "Exceptional service from start to finish. The proactive approach and attention to detail made all the difference in achieving a successful resolution."
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">MT</span>
                      </div>
                      <div>
                        <div className="font-bold">Michael Thompson</div>
                        <div className="text-sm text-gray-400">Technology Entrepreneur</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mt-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-red-500 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Testimonial 6 */}
                <div className="bg-gray-900 p-8 relative">
                  <div className="absolute top-6 left-6">
                    <Quote className="h-8 w-8 text-red-500" />
                  </div>
                  <div className="pt-8">
                    <p className="text-gray-300 mb-6 leading-relaxed">
                      "Professional excellence combined with genuine care for clients. The comprehensive approach and clear guidance were invaluable throughout the process."
                    </p>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">AK</span>
                      </div>
                      <div>
                        <div className="font-bold">Amanda Kim</div>
                        <div className="text-sm text-gray-400">Healthcare Administrator</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 mt-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-red-500 fill-current" />
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Professional Background Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <div className="flex items-center justify-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase mb-4">
                  <div className="w-8 h-px bg-red-500"></div>
                  PROFESSIONAL BACKGROUND
                  <div className="w-8 h-px bg-red-500"></div>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold text-black leading-tight mb-6">
                  Education & <span className="text-red-500">Credentials</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  A strong foundation of legal education, professional certifications, and continuous learning that drives excellence in legal practice.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
                {/* Left - Education & Certifications */}
                <div className="space-y-8">
                  {/* Education */}
                  <div className="bg-white p-8 shadow-lg">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <GraduationCap className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-black">Education</h3>
                    </div>
                    <div className="space-y-4">
                      <div className="border-l-4 border-red-500 pl-6">
                        <h4 className="text-lg font-bold text-black">
                          {user?.education || 'Juris Doctor (J.D.)'}
                        </h4>
                        <p className="text-gray-600">
                          {user?.organization || 'Prestigious Law School'}
                        </p>
                        <p className="text-sm text-gray-500 mt-2">
                          Comprehensive legal education with focus on {user?.lawSpecialization || user?.practiceArea || 'legal practice'}
                        </p>
                      </div>

                      {user?.alumniInformation && (
                        <div className="border-l-4 border-gray-300 pl-6">
                          <h4 className="text-lg font-bold text-black">Alumni Information</h4>
                          <p className="text-gray-600">{user.alumniInformation}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Bar Admission & License */}
                  {user?.barExamStatus === 'Passed' && (
                    <div className="bg-white p-8 shadow-lg">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                          <Certificate className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-black">Bar Admission</h3>
                      </div>
                      <div className="space-y-4">
                        <div className="border-l-4 border-red-500 pl-6">
                          <h4 className="text-lg font-bold text-black">Licensed Attorney</h4>
                          <p className="text-gray-600">
                            {user?.location || 'State Bar'} - Active Status
                          </p>
                          {user?.licenseNumber && (
                            <p className="text-sm text-gray-500 mt-2">
                              License Number: {user.licenseNumber}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Professional Memberships */}
                  {user?.professionalMemberships && (
                    <div className="bg-white p-8 shadow-lg">
                      <div className="flex items-center gap-4 mb-6">
                        <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                          <Users className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-2xl font-bold text-black">Professional Memberships</h3>
                      </div>
                      <div className="border-l-4 border-red-500 pl-6">
                        <p className="text-gray-600 leading-relaxed">
                          {user.professionalMemberships}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Right - Experience & Achievements */}
                <div className="space-y-8">
                  {/* Professional Experience */}
                  <div className="bg-black text-white p-8">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <Briefcase className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold">Professional Experience</h3>
                    </div>
                    <div className="space-y-6">
                      <div className="border-l-4 border-red-500 pl-6">
                        <h4 className="text-lg font-bold">
                          {user?.lawSpecialization || user?.practiceArea || 'Legal Practice'} Specialist
                        </h4>
                        <p className="text-gray-300 mb-2">
                          {user?.yearsOfExperience || 5}+ Years of Experience
                        </p>
                        <p className="text-gray-300 text-sm leading-relaxed">
                          Extensive experience in {user?.lawSpecialization || user?.practiceArea || 'legal matters'} with a proven track record of successful case outcomes and client satisfaction.
                        </p>
                      </div>

                      <div className="border-l-4 border-gray-600 pl-6">
                        <h4 className="text-lg font-bold">Key Achievements</h4>
                        <ul className="text-gray-300 text-sm space-y-2 mt-2">
                          <li>• Successfully handled {stats?.totalViews ? Math.floor(stats.totalViews / 100) + '00+' : '500+'} legal cases</li>
                          <li>• Maintained 98% client satisfaction rate</li>
                          <li>• Published {stats?.totalBlogPosts || 25}+ legal articles and insights</li>
                          <li>• Active contributor to legal community and professional development</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Skills & Expertise */}
                  <div className="bg-white p-8 shadow-lg">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <Target className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-black">Core Competencies</h3>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Legal Research</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Case Analysis</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Contract Drafting</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Litigation Support</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Client Counseling</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Negotiation</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Compliance</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-red-500" />
                          <span className="text-gray-700">Risk Assessment</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="bg-red-500 text-white p-8">
                    <h3 className="text-2xl font-bold mb-6">Get In Touch</h3>
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Mail className="h-5 w-5" />
                        <span>{user?.email || '<EMAIL>'}</span>
                      </div>
                      {user?.phoneNumber && (
                        <div className="flex items-center gap-3">
                          <Phone className="h-5 w-5" />
                          <span>{user.phoneNumber}</span>
                        </div>
                      )}
                      {user?.location && (
                        <div className="flex items-center gap-3">
                          <MapPin className="h-5 w-5" />
                          <span>{user.location}</span>
                        </div>
                      )}
                      {user?.linkedinUrl && (
                        <div className="flex items-center gap-3">
                          <ExternalLink className="h-5 w-5" />
                          <a href={user.linkedinUrl} target="_blank" rel="noopener noreferrer" className="hover:underline">
                            LinkedIn Profile
                          </a>
                        </div>
                      )}
                    </div>
                    <Button
                      className="w-full mt-6 bg-white text-red-500 hover:bg-gray-100 font-semibold"
                      size="lg"
                    >
                      Schedule Consultation
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </>
      )}
    </div>
  );
};

export default DigitalPortfolio;