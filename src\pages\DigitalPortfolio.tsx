import React, { useState, useEffect } from 'react';
import { useAuth, UserRole } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Award,
  GraduationCap,
  Building,
  ExternalLink,
  ArrowLeft,
  UserX,
  Sparkles,
  TrendingUp,
  Heart,
  Share,
  Eye,
  MessageCircle,
  FileText,
  Shield,
  Briefcase,
  Users
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { contributorApi } from '@/services/api';

interface ContributorStats {
  totalBlogPosts: number;
  totalNotes: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  totalComments: number;
  joinDate: string;
  lastActive: string;
  featuredContent: number;
  publishedContent: number;
}

const DigitalPortfolio: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [stats, setStats] = useState<ContributorStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if user has access (Editor or Admin only)
  const hasAccess = user && (user.role === UserRole.EDITOR || user.role === UserRole.ADMIN);

  useEffect(() => {
    if (hasAccess) {
      fetchContributorStats();
    }
  }, [hasAccess]);

  const fetchContributorStats = async () => {
    try {
      setLoading(true);
      const contributorStats = await contributorApi.getStats();
      setStats(contributorStats);
    } catch (error) {
      console.error('Error fetching contributor stats:', error);
      toast({
        title: "Error",
        description: "Failed to load contribution statistics.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lawvriksh-navy/5 to-lawvriksh-burgundy/5 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center border-2 border-lawvriksh-navy/20 shadow-xl">
          <CardHeader className="pb-4">
            <div className="mx-auto w-16 h-16 bg-lawvriksh-burgundy/10 rounded-full flex items-center justify-center mb-4">
              <UserX className="h-8 w-8 text-lawvriksh-burgundy" />
            </div>
            <CardTitle className="legal-heading text-xl text-lawvriksh-navy">
              Access Restricted
            </CardTitle>
            <CardDescription className="legal-text text-lawvriksh-gray">
              Digital Portfolio is only available to Editors and Administrators.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Link to="/dashboard">
              <Button className="w-full bg-lawvriksh-navy hover:bg-lawvriksh-navy/90">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Dashboard
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-lawvriksh-navy/5 to-lawvriksh-burgundy/5">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 rounded-xl bg-lawvriksh-navy/10 border border-lawvriksh-navy/20">
              <Sparkles className="h-6 w-6 text-lawvriksh-navy" />
            </div>
            <div>
              <h1 className="legal-heading text-3xl text-lawvriksh-navy">
                Digital Portfolio
              </h1>
              <p className="legal-text text-lawvriksh-gray">
                Your comprehensive professional profile and contributions
              </p>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-lawvriksh-navy"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Profile Information */}
            <div className="lg:col-span-1 space-y-6">
              {/* Profile Header Card */}
              <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                <CardContent className="p-6">
                  <div className="text-center mb-6">
                    <Avatar className="w-24 h-24 mx-auto mb-4 border-4 border-lawvriksh-navy/20">
                      <AvatarImage src={user?.profilePhoto} alt={user?.fullName} />
                      <AvatarFallback className="bg-lawvriksh-navy text-white text-xl font-bold">
                        {user?.fullName ? getInitials(user.fullName) : 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <h2 className="legal-heading text-2xl text-lawvriksh-navy mb-2">
                      {user?.fullName}
                    </h2>
                    <p className="text-lawvriksh-burgundy font-semibold mb-2">
                      {user?.lawSpecialization || 'Legal Professional'}
                    </p>
                    <Badge variant="outline" className="border-lawvriksh-navy text-lawvriksh-navy">
                      {user?.role === UserRole.ADMIN ? 'Administrator' : 'Editor'}
                    </Badge>
                  </div>

                  {/* Contact Information */}
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-3">
                      <Mail className="h-4 w-4 text-lawvriksh-navy" />
                      <span className="text-gray-700">{user?.email}</span>
                    </div>
                    {user?.phoneNumber && (
                      <div className="flex items-center gap-3">
                        <Phone className="h-4 w-4 text-lawvriksh-navy" />
                        <span className="text-gray-700">{user.phoneNumber}</span>
                      </div>
                    )}
                    {user?.location && (
                      <div className="flex items-center gap-3">
                        <MapPin className="h-4 w-4 text-lawvriksh-navy" />
                        <span className="text-gray-700">{user.location}</span>
                      </div>
                    )}
                    {stats?.joinDate && (
                      <div className="flex items-center gap-3">
                        <Calendar className="h-4 w-4 text-lawvriksh-navy" />
                        <span className="text-gray-700">Joined {formatDate(stats.joinDate)}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Professional Credentials */}
              <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                <CardHeader className="pb-4">
                  <CardTitle className="legal-heading text-lg text-lawvriksh-navy flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Professional Credentials
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {user?.education && (
                    <div className="flex items-start gap-3">
                      <GraduationCap className="h-4 w-4 text-lawvriksh-navy mt-1" />
                      <div>
                        <p className="font-medium text-gray-800">Education</p>
                        <p className="text-sm text-gray-600">{user.education}</p>
                      </div>
                    </div>
                  )}

                  {user?.barExamStatus && user.barExamStatus !== 'Not Applicable' && (
                    <div className="flex items-start gap-3">
                      <Award className="h-4 w-4 text-lawvriksh-navy mt-1" />
                      <div>
                        <p className="font-medium text-gray-800">Bar Exam Status</p>
                        <p className="text-sm text-gray-600">{user.barExamStatus}</p>
                      </div>
                    </div>
                  )}

                  {user?.licenseNumber && (
                    <div className="flex items-start gap-3">
                      <FileText className="h-4 w-4 text-lawvriksh-navy mt-1" />
                      <div>
                        <p className="font-medium text-gray-800">License Number</p>
                        <p className="text-sm text-gray-600">{user.licenseNumber}</p>
                      </div>
                    </div>
                  )}

                  {user?.organization && (
                    <div className="flex items-start gap-3">
                      <Building className="h-4 w-4 text-lawvriksh-navy mt-1" />
                      <div>
                        <p className="font-medium text-gray-800">Organization</p>
                        <p className="text-sm text-gray-600">{user.organization}</p>
                      </div>
                    </div>
                  )}

                  {user?.yearsOfExperience && (
                    <div className="flex items-start gap-3">
                      <Briefcase className="h-4 w-4 text-lawvriksh-navy mt-1" />
                      <div>
                        <p className="font-medium text-gray-800">Experience</p>
                        <p className="text-sm text-gray-600">{user.yearsOfExperience} years</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* External Links */}
              {user?.linkedinUrl && (
                <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="legal-heading text-lg text-lawvriksh-navy flex items-center gap-2">
                      <ExternalLink className="h-5 w-5" />
                      Professional Links
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <a
                      href={user.linkedinUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 hover:border-lawvriksh-navy/30 hover:bg-lawvriksh-navy/5 transition-colors"
                    >
                      <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                        <Users className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-800">LinkedIn Profile</p>
                        <p className="text-sm text-gray-600">View professional network</p>
                      </div>
                      <ExternalLink className="h-4 w-4 text-gray-400 ml-auto" />
                    </a>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Right Column - Contribution Metrics & Activity */}
            <div className="lg:col-span-2 space-y-6">
              {/* Contribution Metrics Overview */}
              <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                <CardHeader className="pb-4">
                  <CardTitle className="legal-heading text-xl text-lawvriksh-navy flex items-center gap-2">
                    <TrendingUp className="h-6 w-6" />
                    Contribution Metrics
                  </CardTitle>
                  <CardDescription className="legal-text text-lawvriksh-gray">
                    Your impact and engagement on the platform
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    {/* Total Views */}
                    <div className="text-center p-4 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200">
                      <Eye className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-blue-700">
                        {stats?.totalViews?.toLocaleString() || '0'}
                      </div>
                      <div className="text-sm text-blue-600 font-medium">Total Views</div>
                    </div>

                    {/* Total Likes */}
                    <div className="text-center p-4 rounded-lg bg-gradient-to-br from-red-50 to-red-100 border border-red-200">
                      <Heart className="h-8 w-8 text-red-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-red-700">
                        {stats?.totalLikes?.toLocaleString() || '0'}
                      </div>
                      <div className="text-sm text-red-600 font-medium">Total Likes</div>
                    </div>

                    {/* Total Shares */}
                    <div className="text-center p-4 rounded-lg bg-gradient-to-br from-green-50 to-green-100 border border-green-200">
                      <Share className="h-8 w-8 text-green-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-green-700">
                        {stats?.totalShares?.toLocaleString() || '0'}
                      </div>
                      <div className="text-sm text-green-600 font-medium">Total Shares</div>
                    </div>

                    {/* Total Comments */}
                    <div className="text-center p-4 rounded-lg bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200">
                      <MessageCircle className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                      <div className="text-2xl font-bold text-purple-700">
                        {stats?.totalComments?.toLocaleString() || '0'}
                      </div>
                      <div className="text-sm text-purple-600 font-medium">Total Comments</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Content Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Blog Posts */}
                <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="legal-heading text-lg text-lawvriksh-navy flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Blog Posts
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Total Published</span>
                        <span className="text-2xl font-bold text-lawvriksh-navy">
                          {stats?.totalBlogPosts || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Featured Content</span>
                        <span className="text-lg font-semibold text-lawvriksh-burgundy">
                          {stats?.featuredContent || 0}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Notes */}
                <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="legal-heading text-lg text-lawvriksh-navy flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Notes
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Total Published</span>
                        <span className="text-2xl font-bold text-lawvriksh-navy">
                          {stats?.totalNotes || 0}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Published Content</span>
                        <span className="text-lg font-semibold text-lawvriksh-burgundy">
                          {stats?.publishedContent || 0}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Professional Bio */}
              {user?.bio && (
                <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="legal-heading text-lg text-lawvriksh-navy flex items-center gap-2">
                      <User className="h-5 w-5" />
                      Professional Bio
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 leading-relaxed">{user.bio}</p>
                  </CardContent>
                </Card>
              )}

              {/* Practice Areas & Specializations */}
              {(user?.practiceArea || user?.lawSpecialization) && (
                <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="legal-heading text-lg text-lawvriksh-navy flex items-center gap-2">
                      <Award className="h-5 w-5" />
                      Areas of Expertise
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {user?.practiceArea && (
                      <div>
                        <p className="font-medium text-gray-800 mb-2">Practice Area</p>
                        <Badge variant="secondary" className="bg-lawvriksh-navy/10 text-lawvriksh-navy border-lawvriksh-navy/20">
                          {user.practiceArea}
                        </Badge>
                      </div>
                    )}
                    {user?.lawSpecialization && (
                      <div>
                        <p className="font-medium text-gray-800 mb-2">Specialization</p>
                        <Badge variant="secondary" className="bg-lawvriksh-burgundy/10 text-lawvriksh-burgundy border-lawvriksh-burgundy/20">
                          {user.lawSpecialization}
                        </Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Activity Summary */}
              {stats?.lastActive && (
                <Card className="border-2 border-lawvriksh-navy/20 shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="legal-heading text-lg text-lawvriksh-navy flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      Activity Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Last Active</span>
                        <span className="font-medium text-gray-800">
                          {formatDate(stats.lastActive)}
                        </span>
                      </div>
                      {stats.joinDate && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Member Since</span>
                          <span className="font-medium text-gray-800">
                            {formatDate(stats.joinDate)}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DigitalPortfolio;