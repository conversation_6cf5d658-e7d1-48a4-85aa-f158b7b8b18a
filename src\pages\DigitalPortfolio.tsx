import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { contributorApi } from '../services/api';
import { UserRole } from '../contexts/AuthContext';
import { Button } from '../components/ui/button';
import { useToast } from '../hooks/use-toast';
import {
  Shield,
  Layout,
  Settings,
  CheckCircle
} from 'lucide-react';

interface ContributorStats {
  totalBlogPosts: number;
  totalNotes: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  totalComments: number;
  joinDate: string;
  lastActive: string;
  featuredContent: number;
  publishedContent: number;
}

type PortfolioTemplate = 'comprehensive' | 'modern-minimal' | 'executive-profile';

interface TemplateOption {
  id: PortfolioTemplate;
  name: string;
  description: string;
  preview: string;
  features: string[];
}

const DigitalPortfolio: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [stats, setStats] = useState<ContributorStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState<PortfolioTemplate | null>(null);
  const [showTemplateSelection, setShowTemplateSelection] = useState(true);

  // Check if user has access (Editor or Admin only)
  const hasAccess = user && (user.role === UserRole.EDITOR || user.role === UserRole.ADMIN);

  // Template options
  const templateOptions: TemplateOption[] = [
    {
      id: 'comprehensive',
      name: 'Comprehensive Legal Landing',
      description: 'Complete professional landing page with services, testimonials, and detailed background',
      preview: '/api/placeholder/400/300',
      features: ['Hero Section', 'Services Showcase', 'Client Testimonials', 'Professional Background', 'Contact Integration']
    },
    {
      id: 'modern-minimal',
      name: 'Modern Minimalist',
      description: 'Clean, contemporary design focusing on essential information and visual hierarchy',
      preview: '/api/placeholder/400/300',
      features: ['Minimalist Design', 'Focus on Content', 'Professional Typography', 'Clean Layout', 'Mobile Optimized']
    },
    {
      id: 'executive-profile',
      name: 'Executive Profile',
      description: 'Corporate-style profile emphasizing leadership, achievements, and professional network',
      preview: '/api/placeholder/400/300',
      features: ['Executive Summary', 'Achievement Highlights', 'Professional Network', 'Leadership Focus', 'Corporate Aesthetic']
    }
  ];

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Load saved template preference
  useEffect(() => {
    const savedTemplate = localStorage.getItem('portfolioTemplate') as PortfolioTemplate;
    if (savedTemplate && templateOptions.find(t => t.id === savedTemplate)) {
      setSelectedTemplate(savedTemplate);
      setShowTemplateSelection(false);
    }
  }, []);

  useEffect(() => {
    if (hasAccess) {
      fetchContributorStats();
    }
  }, [hasAccess]);

  const fetchContributorStats = async () => {
    try {
      setLoading(true);
      const contributorStats = await contributorApi.getStats();
      setStats(contributorStats);
    } catch (error) {
      console.error('Error fetching contributor stats:', error);
      toast({
        title: "Error",
        description: "Failed to load contribution statistics.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: PortfolioTemplate) => {
    setSelectedTemplate(templateId);
    localStorage.setItem('portfolioTemplate', templateId);
    setShowTemplateSelection(false);
    toast({
      title: "Template Selected",
      description: `${templateOptions.find(t => t.id === templateId)?.name} template has been applied.`,
    });
  };

  // Handle template change
  const handleChangeTemplate = () => {
    setShowTemplateSelection(true);
  };

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lawvriksh-navy/5 to-lawvriksh-burgundy/5 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-lg shadow-xl border border-lawvriksh-navy/20 max-w-md">
          <div className="w-16 h-16 bg-lawvriksh-burgundy/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="h-8 w-8 text-lawvriksh-burgundy" />
          </div>
          <h2 className="legal-heading text-2xl text-lawvriksh-navy mb-4">
            Access Restricted
          </h2>
          <p className="legal-text text-lawvriksh-gray mb-6">
            Digital Portfolio access is limited to Editors and Administrators only.
          </p>
          <Button
            variant="outline"
            className="border-lawvriksh-navy text-lawvriksh-navy hover:bg-lawvriksh-navy hover:text-white"
            onClick={() => window.history.back()}
          >
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {loading ? (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading your portfolio...</p>
          </div>
        </div>
      ) : showTemplateSelection ? (
        // Template Selection Interface
        <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
          <div className="container mx-auto px-6 py-20">
            {/* Header */}
            <div className="text-center mb-16">
              <div className="flex items-center justify-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase mb-4">
                <div className="w-8 h-px bg-red-500"></div>
                PORTFOLIO TEMPLATES
                <div className="w-8 h-px bg-red-500"></div>
              </div>
              <h1 className="text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Choose Your <span className="text-red-500">Professional</span> Template
              </h1>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Select from three distinct professional portfolio templates, each designed to showcase your legal expertise and professional profile in a unique way.
              </p>
            </div>

            {/* Template Options */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
              {templateOptions.map((template) => (
                <div key={template.id} className="bg-gray-900 rounded-lg overflow-hidden hover:bg-gray-800 transition-colors duration-300 group">
                  {/* Preview Image */}
                  <div className="aspect-video bg-gray-800 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-red-500/20 to-black/60 flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                          <Layout className="h-8 w-8 text-white" />
                        </div>
                        <p className="text-white font-medium">Preview</p>
                      </div>
                    </div>
                  </div>

                  {/* Template Info */}
                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-3">{template.name}</h3>
                    <p className="text-gray-300 mb-4 leading-relaxed">{template.description}</p>

                    {/* Features */}
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-red-500 mb-2 uppercase tracking-wider">Features</h4>
                      <ul className="space-y-1">
                        {template.features.map((feature, index) => (
                          <li key={index} className="text-sm text-gray-400 flex items-center gap-2">
                            <div className="w-1 h-1 bg-red-500 rounded-full"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Select Button */}
                    <Button
                      onClick={() => handleTemplateSelect(template.id)}
                      className="w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-3 transition-colors duration-300"
                    >
                      Select Template
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {/* Additional Info */}
            <div className="text-center">
              <p className="text-gray-400 mb-4">
                You can change your template selection at any time from your portfolio page.
              </p>
              <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-red-500" />
                  <span>Fully Responsive</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-red-500" />
                  <span>Professional Design</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-red-500" />
                  <span>Complete Profile Integration</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Selected Template Display
        <div className="relative">
          {/* Template Change Button */}
          <div className="fixed top-4 right-4 z-50">
            <Button
              onClick={handleChangeTemplate}
              className="bg-red-500 hover:bg-red-600 text-white shadow-lg"
              size="sm"
            >
              <Settings className="h-4 w-4 mr-2" />
              Change Template
            </Button>
          </div>

          {/* Render Selected Template */}
          <div className="text-center py-20">
            <h2 className="text-3xl font-bold mb-4">Template: {selectedTemplate}</h2>
            <p className="text-gray-600">Template implementation coming next...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default DigitalPortfolio;