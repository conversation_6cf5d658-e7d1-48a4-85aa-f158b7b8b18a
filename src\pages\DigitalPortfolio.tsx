import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { contributorApi } from '../services/api';
import { UserRole } from '../types/auth';
import { Button } from '../components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { useToast } from '../hooks/use-toast';
import {
  Eye,
  Heart,
  Share,
  MessageCircle,
  FileText,
  Award,
  Shield,
  GraduationCap,
  Building,
  Briefcase,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ExternalLink,
  Users,
  User,
  TrendingUp
} from 'lucide-react';

interface ContributorStats {
  totalBlogPosts: number;
  totalNotes: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  totalComments: number;
  joinDate: string;
  lastActive: string;
  featuredContent: number;
  publishedContent: number;
}

const DigitalPortfolio: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [stats, setStats] = useState<ContributorStats | null>(null);
  const [loading, setLoading] = useState(true);

  // Check if user has access (Editor or Admin only)
  const hasAccess = user && (user.role === UserRole.EDITOR || user.role === UserRole.ADMIN);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  useEffect(() => {
    if (hasAccess) {
      fetchContributorStats();
    }
  }, [hasAccess]);

  const fetchContributorStats = async () => {
    try {
      setLoading(true);
      const contributorStats = await contributorApi.getStats();
      setStats(contributorStats);
    } catch (error) {
      console.error('Error fetching contributor stats:', error);
      toast({
        title: "Error",
        description: "Failed to load contribution statistics.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!hasAccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-lawvriksh-navy/5 to-lawvriksh-burgundy/5 flex items-center justify-center">
        <div className="text-center p-8 bg-white rounded-lg shadow-xl border border-lawvriksh-navy/20 max-w-md">
          <div className="w-16 h-16 bg-lawvriksh-burgundy/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shield className="h-8 w-8 text-lawvriksh-burgundy" />
          </div>
          <h2 className="legal-heading text-2xl text-lawvriksh-navy mb-4">
            Access Restricted
          </h2>
          <p className="legal-text text-lawvriksh-gray mb-6">
            Digital Portfolio is available for Editors and Administrators only.
            Please contact your administrator for access.
          </p>
          <Button
            variant="outline"
            className="border-lawvriksh-navy text-lawvriksh-navy hover:bg-lawvriksh-navy hover:text-white"
            onClick={() => window.history.back()}
          >
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="relative bg-black text-white overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-black via-black/90 to-transparent z-10"></div>
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Legal Professional"
            className="w-full h-full object-cover opacity-40"
          />
        </div>

        <div className="relative z-20 container mx-auto px-6 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase">
                  <div className="w-8 h-px bg-red-500"></div>
                  PROFESSIONAL PORTFOLIO
                </div>
                <h1 className="text-5xl lg:text-6xl font-bold leading-tight">
                  LEADING
                  <br />
                  <span className="text-red-500">LEGAL</span>
                  <br />
                  PRACTICE
                </h1>
                <p className="text-xl text-gray-300 leading-relaxed max-w-lg">
                  {user?.lawSpecialization || 'Legal Professional'} with expertise in delivering exceptional legal services and comprehensive solutions.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  className="bg-red-500 hover:bg-red-600 text-white px-8 py-4 text-lg font-semibold"
                >
                  VIEW PORTFOLIO
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-black px-8 py-4 text-lg font-semibold"
                >
                  CONTACT
                </Button>
              </div>

              {/* Stats Preview */}
              <div className="flex items-center gap-8 pt-8 border-t border-gray-700">
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-500">
                    {stats?.totalBlogPosts || 0}
                  </div>
                  <div className="text-sm text-gray-400 uppercase tracking-wider">
                    Publications
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-500">
                    {stats?.totalViews ? Math.floor(stats.totalViews / 1000) + 'K' : '0'}
                  </div>
                  <div className="text-sm text-gray-400 uppercase tracking-wider">
                    Total Views
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-500">
                    {user?.yearsOfExperience || '0'}+
                  </div>
                  <div className="text-sm text-gray-400 uppercase tracking-wider">
                    Years Experience
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content - Professional Photo */}
            <div className="relative">
              <div className="relative">
                <div className="absolute -top-4 -right-4 w-full h-full border-2 border-red-500 z-0"></div>
                <div className="relative z-10 bg-white p-1">
                  <Avatar className="w-full h-96 rounded-none">
                    <AvatarImage
                      src={user?.profilePhoto || "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"}
                      alt={user?.fullName}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-gray-200 text-gray-600 text-6xl font-bold rounded-none h-96">
                      {user?.fullName ? getInitials(user.fullName) : 'LP'}
                    </AvatarFallback>
                  </Avatar>
                </div>

                {/* Floating Stats Card */}
                <div className="absolute -bottom-6 -left-6 bg-red-500 text-white p-6 z-20">
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {stats?.totalLikes || 0}
                    </div>
                    <div className="text-sm uppercase tracking-wider">
                      Client Satisfaction
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500"></div>
        </div>
      ) : (
        <>
          {/* About Section */}
          <section className="py-20 bg-gray-50">
            <div className="container mx-auto px-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                {/* Left - Image */}
                <div className="relative">
                  <div className="relative">
                    <img
                      src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                      alt="Legal Professional"
                      className="w-full h-96 object-cover"
                    />
                    <div className="absolute inset-0 bg-black/20"></div>
                  </div>

                  {/* Floating Badge */}
                  <div className="absolute -bottom-8 left-8 bg-white p-6 shadow-xl">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <Award className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <div className="font-bold text-lg">MISSION OF OUR COMPANY</div>
                        <div className="text-sm text-gray-600">Excellence in Legal Practice</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Right - Content */}
                <div className="space-y-8">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase">
                      <div className="w-8 h-px bg-red-500"></div>
                      About {user?.fullName?.split(' ')[0] || 'Professional'}
                    </div>
                    <h2 className="text-4xl lg:text-5xl font-bold text-black leading-tight">
                      About <span className="text-red-500">LawPursuit</span>
                    </h2>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      {user?.bio || 'Dedicated legal professional committed to delivering exceptional legal services with integrity, expertise, and unwavering dedication to client success.'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Contribution Metrics Section */}
          <section className="py-20 bg-white">
            <div className="container mx-auto px-6">
              <div className="text-center mb-16">
                <div className="flex items-center justify-center gap-2 text-red-500 text-sm font-medium tracking-wider uppercase mb-4">
                  <div className="w-8 h-px bg-red-500"></div>
                  PROFESSIONAL METRICS
                  <div className="w-8 h-px bg-red-500"></div>
                </div>
                <h2 className="text-4xl lg:text-5xl font-bold text-black leading-tight">
                  Contribution <span className="text-red-500">Statistics</span>
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {/* Blog Posts */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <FileText className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalBlogPosts || 0}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Blog Posts
                  </div>
                </div>

                {/* Total Views */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Eye className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalViews ? (stats.totalViews > 1000 ? Math.floor(stats.totalViews / 1000) + 'K' : stats.totalViews) : '0'}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Total Views
                  </div>
                </div>

                {/* Total Likes */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Heart className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalLikes || 0}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Total Likes
                  </div>
                </div>

                {/* Total Shares */}
                <div className="group text-center">
                  <div className="w-20 h-20 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Share className="h-10 w-10 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-black mb-2">
                    {stats?.totalShares || 0}
                  </div>
                  <div className="text-gray-600 uppercase tracking-wider text-sm">
                    Total Shares
                  </div>
                </div>
              </div>
            </div>
          </section>
        </>
      )}
    </div>
  );
};

export default DigitalPortfolio;